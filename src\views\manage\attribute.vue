<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
            <el-form-item label="联系人姓名" prop="contactsName">
                <el-input v-model="queryParams.contactsName" placeholder="请输入联系人姓名" @keyup.enter="getList" />
            </el-form-item>
            <el-form-item label="单位名称" prop="unitName">
                <el-input v-model="queryParams.unitName" placeholder="请输入单位名称" @keyup.enter="getList" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="getList">搜索</el-button>
                <el-button icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="list">
            <el-table-column label="ID" align="center" prop="id" />
            <el-table-column label="联系人姓名" align="center" prop="contactsName" />
            <el-table-column label="单位名称" align="center" prop="unitName" />
            <el-table-column label="微信号" align="center" prop="wechatId" />
            <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip='true' />
            <el-table-column label="操作" width="280" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="primary" icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改联系人对话框 -->
        <el-dialog :title="title" v-model="dialogVisible" width="500px" append-to-body @close="handleClose">
            <el-form ref="dataForm" :model="dataForm" :rules="dataRules" label-width="80px">
                <el-form-item label="联系人姓名" prop="contactsName">
                    <el-input v-model="dataForm.contactsName" placeholder="请输入联系人姓名" />
                </el-form-item>
                <el-form-item label="单位名称" prop="unitName">
                    <el-input v-model="dataForm.unitName" placeholder="请输入单位名称" />
                </el-form-item>
                <el-form-item label="微信号" prop="wechatId">
                    <el-input v-model="dataForm.wechatId" placeholder="请输入微信号" />
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="dataForm.remark" type="textarea" placeholder="请输入备注" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="handleClose">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import { getList, addcontacts, updatecontacts, getcontacts, delcontacts } from "@/api/manage/contacts";
export default {
    data() {
        return {
            showSearch: true,
            isChange: false,
            list: [],
            loading: false,
            queryParams: {
                unitName: '',
                contactsName: '',
                pageNum: 1,
                pageSize: 10
            },
            total: 0,
            dialogVisible: false,
            title: '',
            editId: null,
            dataForm: {
                unitName: "",
                contactsName: "",
                wechatId: "",
                remark: ""
            },
            dataRules: {
                unitName: [
                    { required: true, message: '单位名称不能为空', trigger: 'blur' }
                ],
                contactsName: [
                    { required: true, message: '联系人姓名不能为空', trigger: 'blur' }
                ],
                wechatId: [
                    { required: true, message: '微信号不能为空', trigger: 'blur' }
                ],
                remark: [
                    { required: false, message: '备注不能为空', trigger: 'blur' }
                ]
            }
        }
    },
    mounted() {
        this.getList()
    },
    methods: {
        submitForm() {
            this.$refs.dataForm.validate(async (valid) => {
                if (valid) {
                    if (this.isChange) {
                        let dataForm = JSON.parse(JSON.stringify(this.dataForm));
                        dataForm.id = this.editId;
                        let res = await updatecontacts(dataForm)
                        if (res.code == 200) {
                            this.$message({
                                message: '修改成功',
                                type: 'success'
                            });
                            this.getList()
                            this.dialogVisible = false
                        }
                    } else {
                        let dataForm = JSON.parse(JSON.stringify(this.dataForm));
                        let res = await addcontacts(dataForm)
                        if (res.code == 200) {
                            this.$message({
                                message: '新增成功',
                                type: 'success'
                            });
                            this.getList()
                            this.dialogVisible = false
                        }
                    }
                } else {
                    return false;
                }
            });
        },
        handleAdd() {
            this.isChange = false;
            this.title = "新增联系人";
            this.dialogVisible = true;
            this.$nextTick(() => {
                this.$refs['dataForm'].resetFields();
                this.dataForm = JSON.parse(JSON.stringify(this.$options.data().dataForm))
            })
        },
        handleClose() {
            this.$refs['dataForm'].resetFields();
            this.dataForm = JSON.parse(JSON.stringify(this.$options.data().dataForm))
            this.dialogVisible = false;
        },
        async handleEdit(val) {
            this.title = "修改联系人";
            let res = await getcontacts(val.id)
            if (res.code == 200) {
                this.dataForm = res.data;
                this.editId = val.id; // 设置编辑时的ID
            }
            this.dialogVisible = true;
            this.isChange = true;
        },
        handleDelete(val) {
            this.$confirm('确认删除吗？')
                .then(async (_) => {
                    let res = await delcontacts(val.id)
                    if (res.code == 200) {
                        this.$message({
                            message: '删除成功',
                            type: 'success'
                        });
                        this.getList()
                    }
                })
        },
        reset() {
            this.queryParams = {
                unitName: '',
                contactsName: '',
                pageNum: 1,
                pageSize: 10
            };
            this.getList()
        },
        async getList() {
            this.loading = true;
            let res = await getList(this.queryParams)
            if (res.code == 200) {
                this.total = res.total;
                this.list = res.rows;
                this.loading = false;
            }
        }
    },
}

</script>
<style lang="scss" scoped>
:deep(.el-dialog) {
    background: #FFFFFF;
    border-radius: 14px;
    position: relative;
}
</style>
